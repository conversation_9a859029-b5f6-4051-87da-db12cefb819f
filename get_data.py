import requests, time, csv, re
from bs4 import BeautifulSoup
import pandas as pd

BASE  = "https://www.zolo.ca/ottawa-real-estate"
HEAD  = {"User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Safari/537.36"}
PAGES_TO_PARSE = 2  # first two pages

records = []

for page in range(1, PAGES_TO_PARSE):
    url = BASE + f"/page-{page}"
    html  = requests.get(url, headers=HEAD, timeout=20).text
    soup  = BeautifulSoup(html, "html.parser")

    # every article represents one listing
    for card in soup.select("article"):
        # address & neighbourhood are on the same <a> line, separated by a bullet (•)
        header = card.select_one("a")
        if not header:             # safety – skip odd blocks such as ads
            continue

        full_line   = header.get_text(" ", strip=True)
        try:
            address, neighbourhood = map(str.strip, full_line.split("•", 1))
        except ValueError:
            address, neighbourhood = full_line, ""

        # price and the extra bullet list live in <li> tags
        bullets = [li.get_text(" ", strip=True) for li in card.select("li")]
        price   = next((b for b in bullets if b.startswith("$")), "NA")
        beds    = next((re.sub(r"\s*bed.*", "", b) for b in bullets if "bed" in b), "NA")
        baths   = next((re.sub(r"\s*bath.*", "", b) for b in bullets if "bath" in b), "NA")
        sqft    = next((b for b in bullets if "sqft" in b), "NA")

        mls_id  = None
        mls_tag = card.find(string=re.compile(r"MLS"))
        if mls_tag:
            mls_id = mls_tag.strip()

        records.append(
            {
                "address"      : address,
                "neighbourhood": neighbourhood,
                "price"        : price,
                "beds"         : beds,
                "baths"        : baths,
                "sqft_range"   : sqft,
                "mls_id"       : mls_id,
            }
        )

    time.sleep(2)                  # good-neighbour pause

# 👉 write to CSV
df = pd.DataFrame(records)
df.to_csv("ottawa_zolo_page1_2.csv", index=False)
print(f"Wrote {len(df)} rows to ottawa_zolo_page1_2.csv")
