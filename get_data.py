import requests, time, csv, re
from bs4 import BeautifulSoup
import pandas as pd

BASE  = "https://www.zolo.ca/ottawa-real-estate"
HEAD  = {"User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Safari/537.36"}
PAGES_TO_PARSE = 1  # first two pages

def convert_beds_baths(value):
    """Convert beds/baths from format like '4+1' to '4.5'"""
    if value == "NA" or not value or value == "—":
        return "NA"

    # Handle formats like "2+1" -> "2.5"
    if "+" in value:
        parts = value.split("+")
        if len(parts) == 2 and parts[1].strip() == "1":
            return str(float(parts[0]) + 0.5)

    return value

def convert_sqft_range(sqft_text):
    """Convert sqft range like '1500-2000 sqft' to average number"""
    if sqft_text == "NA" or not sqft_text or sqft_text == "—":
        return "NA"

    # Remove 'sqft' and extract numbers
    sqft_clean = re.sub(r'\s*sqft.*', '', sqft_text)

    # Check if it's a range like "1500-2000"
    if "-" in sqft_clean:
        parts = sqft_clean.split("-")
        if len(parts) == 2:
            try:
                min_sqft = float(parts[0])
                max_sqft = float(parts[1])
                return str(int((min_sqft + max_sqft) / 2))
            except ValueError:
                pass

    # Try to extract just numbers if it's not a range
    numbers = re.findall(r'\d+', sqft_clean)
    if numbers:
        return numbers[0]

    return sqft_clean

def extract_price_number(price_text):
    """Extract numeric price from text like '$542,000' to '542000'"""
    if price_text == "NA" or not price_text:
        return "NA"

    # Remove dollar sign and commas
    price_clean = re.sub(r'[$,]', '', price_text)

    # Extract just the numbers
    numbers = re.findall(r'\d+', price_clean)
    if numbers:
        return numbers[0]

    return "NA"

def clean_mls_id(mls_text):
    """Extract MLS ID number from text like 'MLS® X12334971' to 'X12334971'"""
    if not mls_text:
        return "NA"

    # Remove "MLS®" and whitespace
    mls_clean = re.sub(r'MLS®?\s*', '', mls_text).strip()
    return mls_clean if mls_clean else "NA"

def get_postal_code(address):
    """Get postal code from address using geocoder.ca API"""
    try:
        # Clean the address for URL encoding
        import urllib.parse
        encoded_address = urllib.parse.quote(address)

        # Make request to geocoder.ca
        url = f"https://geocoder.ca/?locate={encoded_address}&geoit=XML&json=1"
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            data = response.json()
            return data.get('postal', 'NA')
        else:
            return 'NA'
    except Exception as e:
        print(f"Error getting postal code for {address}: {e}")
        return 'NA'

records = []

for page in range(1, PAGES_TO_PARSE + 1):
    url = BASE + f"/page-{page}"
    print(f"Scraping page {page}: {url}")
    html = requests.get(url, headers=HEAD, timeout=20).text
    soup = BeautifulSoup(html, "html.parser")

    # Process each listing on this page
    for card in soup.select("article"):
        # Get the address link to extract URL
        address_link = card.select_one("a[href*='/ottawa-real-estate/']")
        if not address_link:
            continue

        href = address_link.get('href', '')
        if href.startswith('http'):
            listing_url = href
        else:
            listing_url = "https://www.zolo.ca" + href

        # Extract address (remove neighbourhood)
        full_line = address_link.get_text(" ", strip=True)
        # Take only the address part (before the bullet if it exists)
        address = full_line.split("•")[0].strip()

        # Extract geo coordinates
        lat_meta = card.select_one('meta[itemprop="latitude"]')
        lng_meta = card.select_one('meta[itemprop="longitude"]')
        latitude = lat_meta.get('content', 'NA') if lat_meta else 'NA'
        longitude = lng_meta.get('content', 'NA') if lng_meta else 'NA'

        # Extract property type from SVG title
        property_type_svg = card.select_one('svg title')
        property_type = property_type_svg.get_text() if property_type_svg else 'NA'

        # Extract price from structured data
        price_span = card.select_one('span[itemprop="price"]')
        if price_span:
            price_value = price_span.get('value', 'NA')
        else:
            # Fallback to text extraction
            price_li = card.select_one('li.price')
            price_value = price_li.get_text() if price_li else 'NA'

        price_clean = extract_price_number(price_value)

        # Skip this listing if there's no valid price
        if price_clean == "NA":
            continue

        # Extract beds, baths, sqft from list items
        bullets = [li.get_text(" ", strip=True) for li in card.select("li")]
        beds_raw = next((re.sub(r"\s*bed.*", "", b) for b in bullets if "bed" in b), "NA")
        baths_raw = next((re.sub(r"\s*bath.*", "", b) for b in bullets if "bath" in b), "NA")
        sqft_raw = next((b for b in bullets if "sqft" in b), "NA")

        # Convert beds and baths
        beds = convert_beds_baths(beds_raw)
        baths = convert_beds_baths(baths_raw)
        sqft = convert_sqft_range(sqft_raw)

        # Extract MLS ID
        mls_span = card.select_one('span:contains("MLS")')
        if not mls_span:
            mls_tag = card.find(string=re.compile(r"MLS"))
            mls_id = clean_mls_id(mls_tag) if mls_tag else "NA"
        else:
            mls_id = clean_mls_id(mls_span.get_text())

        # Get postal code from address
        postal_code = get_postal_code(address)

        records.append({
            "address": address,
            "price": price_clean,
            "beds": beds,
            "baths": baths,
            "sqft": sqft,
            "property_type": property_type,
            "latitude": latitude,
            "longitude": longitude,
            "mls_id": mls_id,
            "postal_code": postal_code,
            "listing_url": listing_url,
        })

    time.sleep(2)  # good-neighbour pause

# 👉 write to CSV
df = pd.DataFrame(records)
df.to_csv("ottawa_zolo_page1_2.csv", index=False)
print(f"Wrote {len(df)} rows to ottawa_zolo_page1_2.csv")
