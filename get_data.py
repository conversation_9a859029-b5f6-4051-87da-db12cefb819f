import requests, time, csv, re, random
from bs4 import BeautifulSoup
import pandas as pd
from tqdm import tqdm
import urllib.parse

BASE = "https://www.zolo.ca/ottawa-real-estate"
PAGES_TO_PARSE = 2  # first two pages

# User-Agent rotation pool
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]

def get_realistic_headers(referer=None):
    """Generate realistic browser headers with rotation"""
    user_agent = random.choice(USER_AGENTS)

    headers = {
        "User-Agent": user_agent,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none" if not referer else "same-origin",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0",
        "DNT": "1"
    }

    if referer:
        headers["Referer"] = referer

    return headers

def random_delay(min_delay=2, max_delay=8):
    """Add random delay between requests"""
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def make_request_with_backoff(url, headers, max_retries=3, base_delay=1):
    """Make HTTP request with exponential backoff on errors"""
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=20)

            if response.status_code == 200:
                return response
            elif response.status_code == 429:  # Rate limited
                wait_time = base_delay * (2 ** attempt) + random.uniform(0, 1)
                print(f"Rate limited. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
                time.sleep(wait_time)
            elif response.status_code in [503, 502, 504]:  # Server errors
                wait_time = base_delay * (2 ** attempt)
                print(f"Server error {response.status_code}. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
                time.sleep(wait_time)
            else:
                print(f"HTTP {response.status_code} error for {url}")
                return None

        except requests.exceptions.RequestException as e:
            wait_time = base_delay * (2 ** attempt)
            print(f"Request failed: {e}. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
            time.sleep(wait_time)

    print(f"Failed to fetch {url} after {max_retries} attempts")
    return None

def convert_beds_baths(value):
    """Convert beds/baths from format like '4+1' to '4.5'"""
    if value == "NA" or not value or value == "—":
        return "NA"

    # Handle formats like "2+1" -> "2.5"
    if "+" in value:
        parts = value.split("+")
        if len(parts) == 2 and parts[1].strip() == "1":
            return str(float(parts[0]) + 0.5)

    return value

def convert_sqft_range(sqft_text):
    """Convert sqft range like '1500-2000 sqft' to average number"""
    if sqft_text == "NA" or not sqft_text or sqft_text == "—":
        return "NA"

    # Remove 'sqft' and extract numbers
    sqft_clean = re.sub(r'\s*sqft.*', '', sqft_text)

    # Check if it's a range like "1500-2000"
    if "-" in sqft_clean:
        parts = sqft_clean.split("-")
        if len(parts) == 2:
            try:
                min_sqft = float(parts[0])
                max_sqft = float(parts[1])
                return str(int((min_sqft + max_sqft) / 2))
            except ValueError:
                pass

    # Try to extract just numbers if it's not a range
    numbers = re.findall(r'\d+', sqft_clean)
    if numbers:
        return numbers[0]

    return sqft_clean

def extract_price_number(price_text):
    """Extract numeric price from text like '$542,000' to '542000'"""
    if price_text == "NA" or not price_text:
        return "NA"

    # Remove dollar sign and commas
    price_clean = re.sub(r'[$,]', '', price_text)

    # Extract just the numbers
    numbers = re.findall(r'\d+', price_clean)
    if numbers:
        return numbers[0]

    return "NA"

def clean_mls_id(mls_text):
    """Extract MLS ID number from text like 'MLS® *********' to '*********'"""
    if not mls_text:
        return "NA"

    # Remove "MLS®" and whitespace
    mls_clean = re.sub(r'MLS®?\s*', '', mls_text).strip()
    return mls_clean if mls_clean else "NA"

def get_postal_code(address):
    """Get postal code from address using geocoder.ca API with enhanced error handling"""
    try:
        # Handle building numbers with dashes
        # Pattern: "2002-203 Catherine Street" -> "203 Catherine Street"
        if re.match(r'^\d+-\d+\s', address):
            parts = address.split('-', 1)
            if len(parts) > 1:
                address_for_lookup = parts[1].strip()
            else:
                address_for_lookup = address
        else:
            address_for_lookup = address

        encoded_address = urllib.parse.quote(address_for_lookup)
        url = f"https://geocoder.ca/?locate={encoded_address}&geoit=XML&json=1"

        # Use realistic headers for geocoder API
        headers = {
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Connection": "keep-alive",
            "DNT": "1"
        }

        # Make request with shorter timeout for API calls
        response = make_request_with_backoff(url, headers, max_retries=2, base_delay=0.5)

        if response and response.status_code == 200:
            data = response.json()
            postal = data.get('postal', 'NA')

            # Add small delay to be respectful to the API
            time.sleep(random.uniform(0.5, 1.5))
            return postal
        else:
            return 'NA'

    except Exception as e:
        # Silently handle errors to avoid cluttering progress output
        return 'NA'

records = []
total_listings_processed = 0

# Initialize progress bar
progress_bar = tqdm(total=PAGES_TO_PARSE, desc="Scraping pages", unit="page")

for page in range(1, PAGES_TO_PARSE + 1):
    url = BASE + f"/page-{page}"

    # Set referer for natural browsing simulation
    referer = BASE if page == 1 else f"{BASE}/page-{page-1}"
    headers = get_realistic_headers(referer=referer)

    # Make request with backoff
    response = make_request_with_backoff(url, headers)

    if not response:
        progress_bar.set_postfix({"Status": f"Failed page {page}"})
        progress_bar.update(1)
        continue

    soup = BeautifulSoup(response.text, "html.parser")
    page_listings = 0

    # Process each listing on this page
    for card in soup.select("article"):
        # Get the address link to extract URL
        address_link = card.select_one("a[href*='/ottawa-real-estate/']")
        if not address_link:
            continue

        href = address_link.get('href', '')
        if href.startswith('http'):
            listing_url = href
        else:
            listing_url = "https://www.zolo.ca" + href

        # Extract address (remove neighbourhood)
        full_line = address_link.get_text(" ", strip=True)
        # Take only the address part (before the bullet if it exists)
        address = full_line.split("•")[0].strip()

        # Extract geo coordinates
        lat_meta = card.select_one('meta[itemprop="latitude"]')
        lng_meta = card.select_one('meta[itemprop="longitude"]')
        latitude = lat_meta.get('content', 'NA') if lat_meta else 'NA'
        longitude = lng_meta.get('content', 'NA') if lng_meta else 'NA'

        # Extract property type from SVG title
        property_type_svg = card.select_one('svg title')
        property_type = property_type_svg.get_text() if property_type_svg else 'NA'

        # Extract price from structured data
        price_span = card.select_one('span[itemprop="price"]')
        if price_span:
            price_value = price_span.get('value', 'NA')
        else:
            # Fallback to text extraction
            price_li = card.select_one('li.price')
            price_value = price_li.get_text() if price_li else 'NA'

        price_clean = extract_price_number(price_value)

        # Skip this listing if there's no valid price
        if price_clean == "NA":
            continue

        # Extract beds, baths, sqft from list items
        bullets = [li.get_text(" ", strip=True) for li in card.select("li")]
        beds_raw = next((re.sub(r"\s*bed.*", "", b) for b in bullets if "bed" in b), "NA")
        baths_raw = next((re.sub(r"\s*bath.*", "", b) for b in bullets if "bath" in b), "NA")
        sqft_raw = next((b for b in bullets if "sqft" in b), "NA")

        # Convert beds and baths
        beds = convert_beds_baths(beds_raw)
        baths = convert_beds_baths(baths_raw)
        sqft = convert_sqft_range(sqft_raw)

        # Extract MLS ID
        mls_span = card.select_one('span:contains("MLS")')
        if not mls_span:
            mls_tag = card.find(string=re.compile(r"MLS"))
            mls_id = clean_mls_id(mls_tag) if mls_tag else "NA"
        else:
            mls_id = clean_mls_id(mls_span.get_text())

        # Get postal code from address
        postal_code = get_postal_code(address)

        records.append({
            "address": address,
            "price": price_clean,
            "beds": beds,
            "baths": baths,
            "sqft": sqft,
            "property_type": property_type,
            "latitude": latitude,
            "longitude": longitude,
            "mls_id": mls_id,
            "postal_code": postal_code,
            "listing_url": listing_url,
        })

        page_listings += 1
        total_listings_processed += 1

    # Update progress bar with current page stats
    progress_bar.set_postfix({
        "Page listings": page_listings,
        "Total listings": total_listings_processed,
        "Valid records": len(records)
    })
    progress_bar.update(1)

    # Random delay between pages to simulate human behavior
    if page < PAGES_TO_PARSE:  # Don't delay after the last page
        random_delay(3, 10)

# Close progress bar
progress_bar.close()

# 👉 write to CSV
df = pd.DataFrame(records)
df.to_csv("ottawa_real_estate.csv", index=False)

# Print final summary
print(f"\n🎉 Scraping completed successfully!")
print(f"📊 Summary:")
print(f"   • Pages processed: {PAGES_TO_PARSE}")
print(f"   • Total listings found: {total_listings_processed}")
print(f"   • Valid records saved: {len(df)}")
if len(df) > 0:
    print(f"   • Records with postal codes: {len(df[df['postal_code'] != 'NA'])}")
print(f"   • Output file: ottawa_real_estate.csv")

if len(df) > 0:
    print(f"\n📈 Data quality:")
    print(f"   • Average price: ${df['price'].astype(int).mean():,.0f}")
    print(f"   • Price range: ${df['price'].astype(int).min():,} - ${df['price'].astype(int).max():,}")
    print(f"   • Property types: {', '.join(df['property_type'].value_counts().head(3).index.tolist())}")
else:
    print(f"\n⚠️  No valid records were collected. Check the website structure or try again later.")
