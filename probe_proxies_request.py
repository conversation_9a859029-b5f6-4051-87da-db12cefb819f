#!/usr/bin/env python3
"""
new proxy

probe_proxies_ipapi.py

What it does:
 - Query ip-api.com/json to show your current (non-proxied) IP info
 - Use proxybroker2 to find HTTPS proxies in Canada with High anonymity
 - Collect 10 working proxies (as validated by proxybroker2)
 - For each proxy, make 10 requests to http://ip-api.com/json and print each response
 - Print a short summary per-proxy (IP:PORT, protocol, anonymity, country, best observed RTT)

Run:
    python probe_proxies_ipapi.py
"""

import asyncio
import time
import json
from typing import List

import requests
# proxybroker2 API
from proxybroker import Broker  # provided by proxybroker2

IP_API_URL = "http://ip-api.com/json"
PROXY_TARGET_COUNT = 10
REQUESTS_PER_PROXY = 10
REQUEST_TIMEOUT = 8  # seconds for each ip-api request

###########################
# Step 0: helper - pretty print JSO<PERSON>
def pretty_print_json(j):
    try:
        return json.dumps(j, indent=2, ensure_ascii=False)
    except Exception:
        return str(j)

###########################
# Step 1: show local IP info (no proxy)
def show_local_ip_info():
    print("=== Your current (direct) IP info from ip-api.com ===")
    try:
        r = requests.get(IP_API_URL, timeout=REQUEST_TIMEOUT)
        r.raise_for_status()
        print(pretty_print_json(r.json()))
    except Exception as e:
        print("Failed to fetch ip-api (direct):", repr(e))
    print()

###########################
# Step 2: use proxybroker2 to find proxies
async def gather_proxies(limit: int = PROXY_TARGET_COUNT, timeout: int = 30) -> List[str]:
    """
    Use proxybroker2's Broker to find working proxies.
    We instruct the broker to search for HTTPS proxies in Canada with High anonymity.
    The broker itself performs connectivity checks; we consume proxies from the queue
    until we have `limit` items or the broker finishes.
    Returns a list of proxies in "host:port" string form.
    """
    queue = asyncio.Queue()
    broker = Broker(queue)

    found = []

    async def consumer():
        # Keep reading proxies pushed onto the queue by broker.find()
        while True:
            proxy = await queue.get()
            if proxy is None:  # broker signals completion by putting None
                break
            # proxy is an object with attributes like host (or ip), port, types, is_working, geo
            host = getattr(proxy, "host", None) or getattr(proxy, "ip", None) or getattr(proxy, "address", None)
            port = getattr(proxy, "port", None)
            if not host or not port:
                continue
            # only collect until we have enough
            if getattr(proxy, "is_working", True):
                found.append((host, port, proxy))
                print(f"[found] {host}:{port}  types={getattr(proxy, 'types', None)}  lvl={getattr(proxy, 'lvl', getattr(proxy,'level',None))}")
            if len(found) >= limit:
                # optional: stop broker early by cancelling its task
                # Note: broker.find will stop by itself when it hits its internal limit,
                # but we also want to stop as soon as we have `limit` proxies.
                break

    # Launch find and consumer concurrently. We request more candidates (limit*4) to have a better chance.
    requested_limit = max(limit * 4, limit)
    find_task = asyncio.create_task(broker.find(types=["HTTPS"], countries=["CA"], lvl="High", limit=requested_limit))
    consumer_task = asyncio.create_task(consumer())

    # wait until consumer finishes or we hit an overall timeout
    try:
        await asyncio.wait_for(asyncio.shield(consumer_task), timeout=timeout)
    except asyncio.TimeoutError:
        print("[warn] gathering proxies timed out")
    finally:
        # if find still running, cancel it gracefully
        if not find_task.done():
            find_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await find_task

        # let queue consumer finish by putting None
        try:
            await queue.put(None)
        except Exception:
            pass

    # convert found tuples into strings and return first `limit`
    proxy_strings = [f"{h}:{p}" for (h, p, _) in found][:limit]
    return proxy_strings

###########################
# Step 3: for each proxy, perform N requests to ip-api and print results
def probe_with_proxies(proxy_list: List[str], requests_per_proxy: int = REQUESTS_PER_PROXY):
    for proxy in proxy_list:
        print("\n" + "="*60)
        print(f"Proxy: {proxy}")
        session = requests.Session()
        # Many HTTP(s) proxies support CONNECT for HTTPS; use http://proxy for both schemes
        proxy_url = f"http://{proxy}"
        session.proxies.update({"http": proxy_url, "https": proxy_url})
        session.headers.update({"User-Agent": "proxy-prober/1.0 (+https://github.com/bluet/proxybroker2)"})
        successes = 0
        times = []
        for i in range(requests_per_proxy):
            try:
                t0 = time.time()
                r = session.get(IP_API_URL, timeout=REQUEST_TIMEOUT)
                elapsed = time.time() - t0
                times.append(elapsed)
                r.raise_for_status()
                j = r.json()
                print(f"[{i+1}/{requests_per_proxy}] {elapsed:.2f}s -> {pretty_print_json(j)}")
                successes += 1
            except Exception as e:
                print(f"[{i+1}/{requests_per_proxy}] failed: {repr(e)}")
        # summary for this proxy
        print("--- proxy summary ---")
        print("Proxy string:", proxy)
        print("Requests succeeded:", successes, "/", requests_per_proxy)
        if times:
            print(f"Observed RTTs (s): min={min(times):.2f}, avg={sum(times)/len(times):.2f}, max={max(times):.2f}")
        else:
            print("No successful requests to compute RTT")
        print("="*60)

###########################
# Main runner (glue)
import contextlib

def main():
    show_local_ip_info()

    print("Finding candidate Canadian HTTPS proxies with High anonymity (via proxybroker2)...")
    try:
        proxies = asyncio.run(gather_proxies(limit=PROXY_TARGET_COUNT, timeout=60))
    except Exception as e:
        print("Error while running proxy gather:", repr(e))
        return

    if not proxies:
        print("No proxies found. Try running the CLI or increasing the timeout/limit.")
        print("Example CLI (works from shell):")
        print("  python -m proxybroker find --types HTTPS --lvl High --countries CA --limit 50")
        return

    print(f"Collected {len(proxies)} proxies (using first {PROXY_TARGET_COUNT}):")
    for p in proxies:
        print(" -", p)

    # now probe each proxy by making requests to ip-api
    probe_with_proxies(proxies, requests_per_proxy=REQUESTS_PER_PROXY)

if __name__ == "__main__":
    main()
