#!/usr/bin/env python3
"""
probe_proxies_ipapi_debug.py

Same behavior as before but with extra diagnostics:
 - TCP connect test to the proxy
 - normal request to http://ip-api.com/json
 - if DNS/Cloudflare error: try numeric-IP fallback with Host header
 - prints full response / error info

Install:
    pip install requests
    pip install requests[socks]   # only if you may use socks proxies
"""
import requests, socket, time, random, json
from typing import Dict, List
import statistics

RAW_URL = "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/countries/CA/data.json"
IP_API_HOST = "ip-api.com"
IP_API_PATH = "/json"
IP_API = f"http://{IP_API_HOST}{IP_API_PATH}"
MAX_PROXIES = 10
REQUESTS_PER_PROXY = 5
REQUEST_TIMEOUT = 10
SLEEP_BETWEEN_REQUESTS = 0.6
RANDOM_JITTER = 0.15
TCP_CONNECT_TIMEOUT = 5.0

def fetch_proxy_list(url: str) -> List[Dict]:
    print("[info] downloading proxy list...")
    r = requests.get(url, timeout=15)
    r.raise_for_status()
    data = r.json()
    if not isinstance(data, list):
        raise RuntimeError("Expected top-level JSON array")
    print(f"[info] fetched {len(data)} entries")
    return data

def filter_elite(entries: List[Dict]) -> List[Dict]:
    return [e for e in entries if str(e.get("anonymity","")).strip().lower()=="elite"]

def normalize_proxy_url(entry: Dict) -> str:
    # prefer provided 'proxy' field; else assemble from protocol/ip/port
    proxy_field = entry.get("proxy")
    if proxy_field:
        return proxy_field.strip()
    proto = (entry.get("protocol") or "http").lower()
    ip = entry.get("ip")
    port = entry.get("port")
    if not ip or not port:
        raise ValueError("missing ip/port")
    url = f"{proto}://{ip}:{port}"
    # make socks5 -> socks5h to request remote DNS (if socks)
    if url.lower().startswith("socks5://") and "socks5h://" not in url.lower():
        url = url.replace("socks5://", "socks5h://")
    return url

def tcp_check(proxy_ip: str, proxy_port: int) -> bool:
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(TCP_CONNECT_TIMEOUT)
    try:
        s.connect((proxy_ip, int(proxy_port)))
        s.close()
        return True
    except Exception as ex:
        # connection failed
        return False

def resolve_host(host: str) -> str:
    # local DNS resolve (may raise socket.gaierror)
    return socket.gethostbyname(host)

def make_request_via_proxy(proxies: Dict[str,str], url: str, headers=None):
    # turn off reading env proxies so system proxy doesn't interfere
    session = requests.Session()
    session.headers.update({"User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Safari/537.36"})
    session.trust_env = False
    try:
        r = session.get(url, proxies=proxies, timeout=REQUEST_TIMEOUT, headers=headers)
        return r, None
    except Exception as ex:
        return None, ex

def probe_proxy(entry: Dict, proxy_url: str):
    print("\n" + "="*80)
    ip = entry.get("ip")
    port = entry.get("port")
    proto = entry.get("protocol")
    anon = entry.get("anonymity")
    geo = entry.get("geolocation") or {}
    print(f"[proxy] {ip}:{port} proto={proto} anon={anon} geo={geo} proxy_url={proxy_url}")

    # quick TCP connect test to proxy itself:
    can_connect = False
    try:
        can_connect = tcp_check(ip, port)
    except Exception:
        can_connect = False
    print(f"[diag] TCP connect to proxy {ip}:{port} -> {'OK' if can_connect else 'FAILED'} (timeout {TCP_CONNECT_TIMEOUT}s)")

    # build proxies dict for requests
    proxies = {"http": proxy_url, "https": proxy_url}

    successes = 0
    durations = []

    for i in range(1, REQUESTS_PER_PROXY+1):
        t0 = time.time()
        r, err = make_request_via_proxy(proxies, IP_API)
        dt = time.time() - t0

        if r is not None:
            durations.append(dt)
            print(f"[{i}/{REQUESTS_PER_PROXY}] direct-host request OK status={r.status_code} time={dt:.2f}s")
            # print body snippet (json or text)
            try:
                print(json.dumps(r.json(), indent=2, ensure_ascii=False))
            except Exception:
                print(r.text[:1000])
            if r.ok:
                successes += 1
        else:
            # error; print it and try a DNS-bypass fallback once for this iteration
            print(f"[{i}/{REQUESTS_PER_PROXY}] direct-host request FAILED after {dt:.2f}s -> {repr(err)}")

            # DNS-bypass: locally resolve ip-api.com and request to numeric IP with Host header
            try:
                numeric_ip = resolve_host(IP_API_HOST)
                fallback_url = f"http://{numeric_ip}{IP_API_PATH}"
                headers = {"Host": IP_API_HOST}
                print(f"  [diag] trying numeric-IP fallback -> {fallback_url} with Host: {IP_API_HOST}")
                t1 = time.time()
                r2, err2 = make_request_via_proxy(proxies, fallback_url, headers=headers)
                dt2 = time.time() - t1
                if r2 is not None:
                    print(f"    [fallback] status={r2.status_code} time={dt2:.2f}s")
                    try:
                        print(json.dumps(r2.json(), indent=2, ensure_ascii=False))
                    except Exception:
                        print(r2.text[:1000])
                    if r2.ok:
                        successes += 1
                        durations.append(dt2)
                else:
                    print(f"    [fallback] FAILED after {dt2:.2f}s -> {repr(err2)}")
            except Exception as rex:
                print(f"  [diag] local DNS resolve failed for {IP_API_HOST}: {repr(rex)}")

        # polite delay
        time.sleep(max(0, SLEEP_BETWEEN_REQUESTS + random.uniform(-RANDOM_JITTER, RANDOM_JITTER)))

    # summary
    print("--- summary ---")
    print(f"successful requests: {successes}/{REQUESTS_PER_PROXY}")
    if durations:
        print(f"RTT(s): min={min(durations):.2f} avg={statistics.mean(durations):.2f} max={max(durations):.2f}")
    else:
        print("no successful requests recorded")
    print("="*80 + "\n")

def main():
    all_entries = fetch_proxy_list(RAW_URL)
    elite = filter_elite(all_entries)
    if not elite:
        print("[error] no elite proxies found")
        return

    random.shuffle(elite)
    chosen = elite[:MAX_PROXIES]
    print(f"[info] probing {len(chosen)} proxies (each up to {REQUESTS_PER_PROXY} requests)")

    for idx, entry in enumerate(chosen, start=1):
        try:
            proxy_url = normalize_proxy_url(entry)
        except Exception as ex:
            print(f"[warn] skipping proxy #{idx} due to error: {ex}")
            continue
        print(f"\n>> Proxy #{idx}/{len(chosen)}")
        probe_proxy(entry, proxy_url)

if __name__ == "__main__":
    import random, statistics
    main()
