import re

def test_address_parsing():
    """Test the address parsing logic for postal code lookup"""
    
    test_addresses = [
        "2002-203 Catherine Street , Ottawa , ON",
        "1505-245 Kent Street , Ottawa , ON", 
        "64 Songbird Private , Ottawa , ON",
        "391 Leavoy Lane , Ottawa , ON",
        "145 Vaughan Street , Ottawa , ON"
    ]
    
    for address in test_addresses:
        print(f"Original: {address}")
        
        # Handle building numbers with dashes
        if re.match(r'^\d+-\d+\s', address):
            # Extract the part after the first dash
            parts = address.split('-', 1)
            if len(parts) > 1:
                address_for_lookup = parts[1].strip()
            else:
                address_for_lookup = address
        else:
            address_for_lookup = address
            
        print(f"For lookup: {address_for_lookup}")
        print("---")

if __name__ == "__main__":
    test_address_parsing()
